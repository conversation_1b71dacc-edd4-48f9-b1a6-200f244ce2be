import { useState, useCallback, useRef, useEffect } from 'react'
import { useRequestDeduplication } from '../utils/requestDeduplicator'

/**
 * Custom hook for lazy data loading with request deduplication
 * Prevents infinite loops and duplicate API calls
 */
export const useLazyDataLoader = () => {
  const [loading, setLoading] = useState({})
  const [data, setData] = useState({})
  const [errors, setErrors] = useState({})
  const loadingRef = useRef({})

  // Use refs to avoid recreating functions
  const deduplicatorRef = useRef(useRequestDeduplication())

  // Generic data loader with deduplication
  const loadData = useCallback(async (key, apiCall, params = {}) => {
    // Prevent duplicate calls
    if (loadingRef.current[key]) {
      console.log(`⏳ Already loading ${key}, skipping duplicate call`)
      return
    }

    try {
      // Set loading state
      setLoading(prev => ({ ...prev, [key]: true }))
      loadingRef.current[key] = true

      // Generate deduplication key
      const requestKey = deduplicatorRef.current.generateKey(key, params)

      // Execute with deduplication
      const response = await deduplicatorRef.current.execute(requestKey, apiCall)

      if (response.success) {
        // console.log(`✅ Successfully loaded ${key}:`, response.data) // Reduced logging
        setData(prev => ({ ...prev, [key]: response.data }))
        setErrors(prev => ({ ...prev, [key]: null }))
      } else {
        throw new Error(response.message || `Failed to load ${key}`)
      }
    } catch (error) {
      console.error(`Error loading ${key}:`, error)
      setErrors(prev => ({ ...prev, [key]: error.message }))
    } finally {
      setLoading(prev => ({ ...prev, [key]: false }))
      loadingRef.current[key] = false
    }
  }, [])

  // Clear data for a specific key
  const clearData = useCallback((key) => {
    setData(prev => {
      const newData = { ...prev }
      delete newData[key]
      return newData
    })
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[key]
      return newErrors
    })
  }, [])

  // Clear all data
  const clearAllData = useCallback(() => {
    setData({})
    setErrors({})
    loadingRef.current = {}
  }, [])

  // Check if data exists for a key
  const hasData = useCallback((key) => {
    return data[key] !== undefined && data[key] !== null
  }, [data])

  // Get data for a key with fallback
  const getData = useCallback((key, fallback = null) => {
    return data[key] || fallback
  }, [data])

  // Check if loading for a key
  const isLoading = useCallback((key) => {
    return loading[key] || false
  }, [loading])

  // Get error for a key
  const getError = useCallback((key) => {
    return errors[key] || null
  }, [errors])

  return {
    loadData,
    clearData,
    clearAllData,
    hasData,
    getData,
    isLoading,
    getError,
    loading,
    data,
    errors
  }
}

/**
 * Hook specifically for admin dashboard data loading
 */
export const useAdminDataLoader = () => {
  const dataLoader = useLazyDataLoader()

  // Create stable references to avoid infinite re-renders
  const loadDataRef = useRef(dataLoader.loadData)
  loadDataRef.current = dataLoader.loadData

  // Specific loaders for admin sections - now stable
  const loadDashboardStats = useCallback((adminService) => {
    return loadDataRef.current('dashboardStats', () => adminService.getDashboardStats())
  }, [])

  const loadAppointments = useCallback((adminService, params = { page: 1, limit: 20 }) => {
    return loadDataRef.current('appointments', () => adminService.getAppointments(params), params)
  }, [])

  const loadCustomers = useCallback((adminService, params = { page: 1, limit: 20 }) => {
    return loadDataRef.current('customers', () => adminService.getCustomers(params), params)
  }, [])

  const loadOrders = useCallback((adminService, params = { page: 1, limit: 20 }) => {
    return loadDataRef.current('orders', () => adminService.getOrders(params), params)
  }, [])

  const loadProducts = useCallback((adminService, params = { page: 1, limit: 20 }) => {
    return loadDataRef.current('products', () => adminService.getProducts(params), params)
  }, [])

  const loadServices = useCallback((adminService, params = { page: 1, limit: 20 }) => {
    return loadDataRef.current('services', () => adminService.getServices(params), params)
  }, [])

  // Load data based on tab - now stable
  const loadDataForTab = useCallback(async (tabId, adminService) => {
    switch (tabId) {
      case 'overview':
        // Overview needs dashboard stats and sample data from all sections
        await Promise.all([
          loadDashboardStats(adminService),
          loadAppointments(adminService, { page: 1, limit: 5 }),
          loadCustomers(adminService, { page: 1, limit: 5 }),
          loadOrders(adminService, { page: 1, limit: 5 }),
          loadProducts(adminService, { page: 1, limit: 5 }),
          loadServices(adminService, { page: 1, limit: 5 })
        ])
        break
      case 'appointments':
        // Load appointments along with services and customers for editing
        await Promise.all([
          loadAppointments(adminService),
          loadServices(adminService),
          loadCustomers(adminService)
        ])
        break
      case 'customers':
        return loadCustomers(adminService)
      case 'orders':
        return loadOrders(adminService)
      case 'products':
        return loadProducts(adminService)
      case 'services':
        return loadServices(adminService)
      default:
        return Promise.resolve()
    }
  }, [loadDashboardStats, loadAppointments, loadCustomers, loadOrders, loadProducts, loadServices])

  return {
    ...dataLoader,
    loadDashboardStats,
    loadAppointments,
    loadCustomers,
    loadOrders,
    loadProducts,
    loadServices,
    loadDataForTab
  }
}
