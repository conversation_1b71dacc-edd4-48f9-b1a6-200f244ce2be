import React, { useState } from 'react'
import { FiX, FiEdit3, FiTrash2, FiUser, FiMail, FiPhone, FiMapPin, FiCalendar, FiShoppingBag } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const CustomerDetailModal = ({ 
  customer, 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete,
  showToast 
}) => {
  const { branding } = useBranding()
  const [isDeleting, setIsDeleting] = useState(false)

  if (!isOpen || !customer) return null

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this customer?')) return
    
    setIsDeleting(true)
    try {
      await onDelete(customer._id)
      showToast('Customer deleted successfully', 'success')
      onClose()
    } catch (error) {
      showToast('Failed to delete customer', 'error')
    } finally {
      setIsDeleting(false)
    }
  }

  const customerName = customer.name || `${customer.firstName || ''} ${customer.lastName || ''}`.trim()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div 
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: branding?.colors?.primary + '20' || '#3B82F620' }}
            >
              <FiUser className="w-5 h-5" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Customer Details</h2>
              <p className="text-sm text-gray-500">
                ID: {customer._id?.slice(-8) || 'N/A'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => onEdit(customer)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
            >
              <FiEdit3 className="w-4 h-4" />
              <span>Edit</span>
            </button>
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
            >
              <FiTrash2 className="w-4 h-4" />
              <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
            </button>
          </div>

          {/* Personal Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiUser className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Personal Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                <div className="text-gray-900 font-medium">
                  {customerName || 'N/A'}
                </div>
              </div>
              {customer.firstName && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">First Name</label>
                  <div className="text-gray-900">
                    {customer.firstName}
                  </div>
                </div>
              )}
              {customer.lastName && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Last Name</label>
                  <div className="text-gray-900">
                    {customer.lastName}
                  </div>
                </div>
              )}
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Email</label>
                <div className="text-gray-900 flex items-center">
                  <FiMail className="w-4 h-4 mr-2 text-gray-500" />
                  {customer.email || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Phone</label>
                <div className="text-gray-900 flex items-center">
                  <FiPhone className="w-4 h-4 mr-2 text-gray-500" />
                  {customer.phone || 'N/A'}
                </div>
              </div>
              {customer.dateOfBirth && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Date of Birth</label>
                  <div className="text-gray-900">
                    {new Date(customer.dateOfBirth).toLocaleDateString()}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Address Information */}
          {(customer.address || customer.city || customer.state || customer.zipCode) && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiMapPin className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Address Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {customer.address && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-600 mb-1">Street Address</label>
                    <div className="text-gray-900">
                      {customer.address}
                    </div>
                  </div>
                )}
                {customer.city && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">City</label>
                    <div className="text-gray-900">
                      {customer.city}
                    </div>
                  </div>
                )}
                {customer.state && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">State</label>
                    <div className="text-gray-900">
                      {customer.state}
                    </div>
                  </div>
                )}
                {customer.zipCode && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">ZIP Code</label>
                    <div className="text-gray-900">
                      {customer.zipCode}
                    </div>
                  </div>
                )}
                {customer.country && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Country</label>
                    <div className="text-gray-900">
                      {customer.country}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Account Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Account Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Customer Since</label>
                <div className="text-gray-900">
                  {customer.createdAt ? new Date(customer.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                <div className="text-gray-900">
                  {customer.updatedAt ? new Date(customer.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'N/A'}
                </div>
              </div>
              {customer.status && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                  <div>
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      customer.status === 'active' 
                        ? 'bg-green-100 text-green-800'
                        : customer.status === 'inactive'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {customer.status}
                    </span>
                  </div>
                </div>
              )}
              {customer.totalOrders !== undefined && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Total Orders</label>
                  <div className="text-gray-900 flex items-center">
                    <FiShoppingBag className="w-4 h-4 mr-2 text-gray-500" />
                    {customer.totalOrders}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {customer.notes && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Notes
              </h3>
              <div className="text-gray-900 bg-white p-3 rounded border">
                {customer.notes}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default CustomerDetailModal
