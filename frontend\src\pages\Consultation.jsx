import { useState, useEffect } from 'react'
import { FiCalendar, FiClock, FiUser, FiMail, FiPhone, FiMessageSquare, FiLock, FiX, FiAlertCircle, FiEye, FiEyeOff } from 'react-icons/fi'
import { serviceService, consultationService, authService } from '../services'
import { useBranding } from '../contexts/BrandingContext'
import PhoneInput from '../components/PhoneInput'

const Consultation = ({ user, userProfile }) => {
  const { branding } = useBranding()
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [currentUser, setCurrentUser] = useState(null)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    service: '',
    date: '',
    time: '',
    message: ''
  })

  const [services, setServices] = useState([])
  const [availableSlots, setAvailableSlots] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingServices, setIsLoadingServices] = useState(true)
  const [error, setError] = useState('')
  const [toast, setToast] = useState(null)
  const [showPassword, setShowPassword] = useState(false)

  // Check if user is logged in and load their data
  useEffect(() => {
    const checkUserAuth = async () => {
      try {
        // Check if user is passed from App.jsx or if authenticated via authService
        if (user && userProfile) {
          // User data is available from App.jsx
          setIsLoggedIn(true)
          setCurrentUser(userProfile)

          // Pre-populate form with user data
          setFormData(prevData => ({
            ...prevData,
            firstName: userProfile.firstName || '',
            lastName: userProfile.lastName || '',
            email: userProfile.email || '',
            phone: userProfile.phone || '',
            // Don't pre-populate password for security
            password: ''
          }))
        } else if (authService.isAuthenticated()) {
          // Fallback to authService if user props are not available
          const authUser = await authService.getCurrentUser()
          if (authUser) {
            setIsLoggedIn(true)
            setCurrentUser(authUser)

            // Pre-populate form with user data
            setFormData(prevData => ({
              ...prevData,
              firstName: authUser.firstName || '',
              lastName: authUser.lastName || '',
              email: authUser.email || '',
              phone: authUser.phone || '',
              // Don't pre-populate password for security
              password: ''
            }))
          }
        } else {
          setIsLoggedIn(false)
          setCurrentUser(null)
        }
      } catch (error) {
        console.error('Error checking user authentication:', error)
        setIsLoggedIn(false)
        setCurrentUser(null)
      }
    }

    checkUserAuth()
  }, [user, userProfile])

  // Load services on component mount
  useEffect(() => {
    const loadServices = async () => {
      try {
        setIsLoadingServices(true)
        const response = await serviceService.getServices()

        if (response.success) {
          setServices(response.data)
        }
      } catch (error) {
        console.error('Error loading services:', error)
        setError('Failed to load services')
      } finally {
        setIsLoadingServices(false)
      }
    }

    loadServices()
  }, [])

  // Load available time slots when date changes
  useEffect(() => {
    const loadAvailableSlots = async () => {
      if (!formData.date) {
        setAvailableSlots([])
        return
      }

      try {
        const response = await appointmentService.getAvailability({
          date: formData.date
        })

        if (response.success) {
          setAvailableSlots(response.data.availableSlots || [])
        }
      } catch (error) {
        console.error('Error loading availability:', error)
        setAvailableSlots([])
      }
    }

    loadAvailableSlots()
  }, [formData.date])

  // Helper functions
  const showToast = (message, type = 'success') => {
    setToast({ message, type })
    setTimeout(() => setToast(null), 3000)
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  // Helper function to convert 12-hour time to 24-hour format
  const convertTo24Hour = (time12h) => {
    if (!time12h) return ''

    const [time, modifier] = time12h.split(' ')
    let [hours, minutes] = time.split(':')

    if (hours === '12') {
      hours = '00'
    }

    if (modifier === 'PM') {
      hours = parseInt(hours, 10) + 12
    }

    return `${hours.toString().padStart(2, '0')}:${minutes}`
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validate required fields (password is only required for non-logged-in users)
    const requiredFields = ['firstName', 'lastName', 'email', 'service', 'date', 'time']
    const missingFields = requiredFields.filter(field => !formData[field])

    if (missingFields.length > 0) {
      showToast('Please fill in all required fields.', 'error')
      return
    }

    setIsLoading(true)

    try {
      let response

      // Convert time to 24-hour format for backend
      const time24h = convertTo24Hour(formData.time)

      if (isLoggedIn) {
        // For logged-in users, use authenticated consultation booking
        const appointmentData = {
          name: `${formData.firstName} ${formData.lastName}`.trim(),
          email: formData.email,
          phone: formData.phone,
          service: formData.service,
          date: formData.date,
          time: time24h,
          message: formData.message
        }

        // Use authenticated consultation service for logged-in users
        response = await consultationService.bookAuthenticatedConsultation(appointmentData)
      } else {
        // For non-logged-in users, use the smart consultation service
        const consultationData = {
          name: `${formData.firstName} ${formData.lastName}`.trim(),
          email: formData.email,
          phone: formData.phone,
          password: formData.password, // Include password for new user creation
          service: formData.service,
          date: formData.date,
          time: time24h,
          message: formData.message
        }

        response = await consultationService.smartBookConsultation(consultationData)
      }

      if (response.success) {
        let message = isLoggedIn
          ? 'Appointment booked successfully! We will contact you soon.'
          : 'Consultation booked successfully! We will contact you soon.'

        // If a new user was created, show additional message
        if (!isLoggedIn && response.data?.isNewUser) {
          message += ' An account has been created for you and you are now logged in.'
        }

        showToast(message, 'success')

        // Reset form after successful submission (but keep user data for logged-in users)
        if (isLoggedIn) {
          // Only reset booking-specific fields for logged-in users
          setFormData(prevData => ({
            ...prevData,
            service: '',
            date: '',
            time: '',
            message: ''
          }))
        } else {
          // Reset all fields for non-logged-in users
          setFormData({
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            password: '',
            service: '',
            date: '',
            time: '',
            message: ''
          })
        }
      } else {
        showToast(response.message || 'Failed to book consultation. Please try again.', 'error')
      }
    } catch (error) {
      console.error('Error booking consultation:', error)

      // Handle specific error messages
      if (error.response?.data?.message) {
        showToast(error.response.data.message, 'error')
      } else {
        showToast('Failed to book consultation. Please try again.', 'error')
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Default time slots (fallback if API doesn't provide them)
  const defaultTimeSlots = [
    '9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM'
  ]

  const timeSlots = availableSlots.length > 0 ? availableSlots : defaultTimeSlots



  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {isLoggedIn ? 'Book Your Appointment' : branding.content.consultationTitle}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {isLoggedIn
              ? 'Schedule your next hair care appointment with us'
              : branding.content.consultationSubtitle
            }
          </p>
          <p className="text-lg text-gray-500 max-w-xl mx-auto mt-4">
            {isLoggedIn
              ? 'Choose your preferred service, date, and time. We\'ll take care of the rest!'
              : branding.content.consultationDescription
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Consultation Info */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">What to Expect</h2>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FiUser className="w-6 h-6" style={{ color: '#008000' }} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Personal Assessment</h3>
                  <p className="text-gray-600">
                    We'll examine your hair type, texture, and current condition to recommend the best loc style for you.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <FiMessageSquare className="w-6 h-6" style={{ color: '#f3d016' }} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Style Discussion</h3>
                  <p className="text-gray-600">
                    Discuss your lifestyle, maintenance preferences, and desired look to create a customized plan.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <FiCalendar className="w-6 h-6" style={{ color: '#008000' }} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Treatment Plan</h3>
                  <p className="text-gray-600">
                    Receive a detailed timeline and care plan for your loc journey, including maintenance schedules.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8 p-6 bg-yellow-50 rounded-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Consultation Details</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <FiClock className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                  Duration: 45-60 minutes
                </li>
                <li className="flex items-center">
                  <FiCalendar className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                  Price: $50 (Applied to service if booked)
                </li>
              </ul>
            </div>
          </div>

          {/* Booking Form */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-gray-900">
                {isLoggedIn ? 'Appointment Details' : branding.content.consultationFormTitle}
              </h2>
              <p className="text-gray-600 mt-2">
                {isLoggedIn
                  ? 'Fill in your appointment preferences below'
                  : branding.content.consultationFormSubtitle
                }
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
                <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      required
                      value={formData.firstName}
                      onChange={handleChange}
                      readOnly={isLoggedIn}
                      className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                        isLoggedIn ? 'bg-gray-50 cursor-not-allowed' : ''
                      }`}
                      style={{ focusRingColor: '#f3d016', focusBorderColor: 'transparent' }}
                      onFocus={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.outline = 'none'
                          e.target.style.borderColor = '#f3d016'
                          e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                        }
                      }}
                      onBlur={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.borderColor = '#d1d5db'
                          e.target.style.boxShadow = 'none'
                        }
                      }}
                      placeholder="Enter your first name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <div className="relative">
                    <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      required
                      value={formData.lastName}
                      onChange={handleChange}
                      readOnly={isLoggedIn}
                      className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                        isLoggedIn ? 'bg-gray-50 cursor-not-allowed' : ''
                      }`}
                      style={{ focusRingColor: '#f3d016', focusBorderColor: 'transparent' }}
                      onFocus={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.outline = 'none'
                          e.target.style.borderColor = '#f3d016'
                          e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                        }
                      }}
                      onBlur={(e) => {
                        if (!isLoggedIn) {
                          e.target.style.borderColor = '#d1d5db'
                          e.target.style.boxShadow = 'none'
                        }
                      }}
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <div className="relative">
                  <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    readOnly={isLoggedIn}
                    className={`w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 ${
                      isLoggedIn ? 'bg-gray-50 cursor-not-allowed' : ''
                    }`}
                    onFocus={(e) => {
                      if (!isLoggedIn) {
                        e.target.style.outline = 'none'
                        e.target.style.borderColor = '#f3d016'
                        e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                      }
                    }}
                    onBlur={(e) => {
                      if (!isLoggedIn) {
                        e.target.style.borderColor = '#d1d5db'
                        e.target.style.boxShadow = 'none'
                      }
                    }}
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <PhoneInput
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={(value) => setFormData(prev => ({ ...prev, phone: value }))}
                  required
                  readOnly={isLoggedIn}
                  placeholder="Enter your phone number"
                  onFocus={(e) => {
                    if (!isLoggedIn) {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = '#f3d016'
                      e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                    }
                  }}
                  onBlur={(e) => {
                    if (!isLoggedIn) {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }
                  }}
                />
              </div>

              {/* Only show password field for non-logged-in users */}
              {!isLoggedIn && (
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Password (Optional - we'll create one for you if left blank)
                  </label>
                  <div className="relative">
                    <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                      onFocus={(e) => {
                        e.target.style.outline = 'none'
                        e.target.style.borderColor = '#f3d016'
                        e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = '#d1d5db'
                        e.target.style.boxShadow = 'none'
                      }}
                      placeholder="Leave blank to auto-generate a secure password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                      title={showPassword ? 'Hide password' : 'Show password'}
                    >
                      {showPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>
              )}

              {/* Show user info for logged-in users */}
              {isLoggedIn && currentUser && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <FiUser className="text-green-600 w-5 h-5 mr-2" />
                    <span className="text-green-800 font-medium">Booking as: {currentUser.firstName} {currentUser.lastName}</span>
                  </div>
                  <p className="text-green-700 text-sm">
                    Your account information has been automatically filled in. You can modify the details above if needed.
                  </p>
                </div>
              )}

              <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Interest *
                </label>
                <select
                  id="service"
                  name="service"
                  required
                  value={formData.service}
                  onChange={handleChange}
                  disabled={isLoadingServices}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = '#f3d016'
                    e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                >
                  <option value="">
                    {isLoadingServices ? 'Loading services...' : 'Select a service'}
                  </option>
                  {services.map((service) => (
                    <option key={service._id} value={service._id}>
                      {service.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Date *
                  </label>
                  <input
                    type="date"
                    id="date"
                    name="date"
                    required
                    value={formData.date}
                    onChange={handleChange}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                    onFocus={(e) => {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = '#f3d016'
                      e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }}
                  />
                </div>

                <div>
                  <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Time *
                  </label>
                  <select
                    id="time"
                    name="time"
                    required
                    value={formData.time}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                    onFocus={(e) => {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = '#f3d016'
                      e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }}
                  >
                    <option value="">Select time</option>
                    {timeSlots.map((time) => (
                      <option key={time} value={time}>
                        {time}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = '#f3d016'
                    e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Tell us about your hair goals, any concerns, or special requests..."
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-6 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: '#f3d016' }}
                onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = '#d4b014')}
                onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = '#f3d016')}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Booking...
                  </>
                ) : (
                  <>
                    <FiCalendar className="w-5 h-5 mr-2" />
                    {isLoggedIn ? 'Book Appointment' : 'Book Consultation'}
                  </>
                )}
              </button>
            </form>
          </div>
        </div>



        {/* Toast Notification */}
        {toast && (
          <div className="fixed top-4 right-4 z-50">
            <div
              className={`px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 ${
                toast.type === 'success'
                  ? 'text-white'
                  : toast.type === 'error'
                  ? 'bg-red-500 text-white'
                  : 'bg-blue-500 text-white'
              }`}
              style={toast.type === 'success' ? { backgroundColor: '#f3d016' } : {}}
            >
              <div className="flex-shrink-0">
                {toast.type === 'success' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
                {toast.type === 'error' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <span className="font-medium">{toast.message}</span>
              <button
                onClick={() => setToast(null)}
                className="flex-shrink-0 ml-4 text-white hover:text-gray-200"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Consultation
