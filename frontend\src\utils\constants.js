// Note: Brand colors now come from API only - no hardcoded values
// Gray colors for UI elements only (not branding)
export const GRAY_COLORS = {
  50: '#f9fafb',
  100: '#f3f4f6',
  200: '#e5e7eb',
  300: '#d1d5db',
  400: '#9ca3af',
  500: '#6b7280',
  600: '#4b5563',
  700: '#374151',
  800: '#1f2937',
  900: '#111827'
}

// Common button styles
export const BUTTON_STYLES = {
  primary: `
    inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white 
    rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    hover:shadow-lg transform hover:-translate-y-0.5
  `,
  secondary: `
    inline-flex items-center justify-center px-6 py-3 text-base font-medium 
    bg-white rounded-lg transition-all duration-200 border-2
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    hover:shadow-lg transform hover:-translate-y-0.5
  `,
  ghost: `
    inline-flex items-center justify-center px-6 py-3 text-base font-medium 
    bg-transparent rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    hover:bg-gray-100
  `
}

// Common spacing
export const SPACING = {
  section: 'py-20',
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  grid: {
    cols1: 'grid grid-cols-1',
    cols2: 'grid grid-cols-1 md:grid-cols-2',
    cols3: 'grid grid-cols-1 md:grid-cols-3',
    cols4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }
}

// Animation classes
export const ANIMATIONS = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  bounce: 'animate-bounce',
  pulse: 'animate-pulse',
  spin: 'animate-spin'
}

// Common shadows
export const SHADOWS = {
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
  card: 'shadow-lg hover:shadow-xl transition-shadow duration-200'
}

// Responsive breakpoints (for reference)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

// Common form styles
export const FORM_STYLES = {
  input: `
    w-full px-4 py-3 border border-gray-300 rounded-lg 
    transition-colors duration-200
    focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200
    placeholder-gray-400
  `,
  textarea: `
    w-full px-4 py-3 border border-gray-300 rounded-lg 
    transition-colors duration-200 resize-vertical
    focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200
    placeholder-gray-400
  `,
  select: `
    w-full px-4 py-3 border border-gray-300 rounded-lg 
    transition-colors duration-200 bg-white
    focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200
  `,
  label: 'block text-sm font-medium text-gray-700 mb-2',
  error: 'text-sm text-red-600 mt-1',
  helper: 'text-sm text-gray-500 mt-1'
}

// Common card styles
export const CARD_STYLES = {
  base: 'bg-white rounded-2xl overflow-hidden',
  shadow: 'bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-200',
  bordered: 'bg-white rounded-2xl overflow-hidden border border-gray-200',
  elevated: 'bg-white rounded-2xl overflow-hidden shadow-xl'
}

// Note: All branding data now comes from API only - no hardcoded fallbacks

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NODE_ENV === 'production'
    ? 'https://your-production-api.com/api'
    : 'http://localhost:3000/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
}





