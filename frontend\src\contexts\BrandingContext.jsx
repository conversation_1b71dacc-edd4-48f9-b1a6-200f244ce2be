import { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { brandingService } from '../services'

// No hardcoded fallbacks - all data comes from API only
const BrandingContext = createContext({
  branding: null,
  isLoading: true,
  error: null,
  refreshBranding: () => {},
  updateBranding: () => {}
})

export const BrandingProvider = ({ children }) => {
  const [branding, setBranding] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pollInterval, setPollInterval] = useState(null)

  // Load branding data from API - NO FALLBACKS
  const loadBranding = useCallback(async () => {
    try {
      setError(null)

      const response = await brandingService.getCompleteBranding()

      if (response.success && response.data) {
        const { branding: brandingData, business, theme, site } = response.data

        // Use ONLY API data - no fallbacks or defaults
        const apiBranding = {
          // Business info from API only
          businessName: business?.name || '',
          tagline: business?.tagline || '',
          description: business?.description || '',
          phone: business?.phone || '',
          email: business?.email || '',
          address: business?.address || '',
          social: business?.social || {},
          hours: business?.hours || {},

          // Theme from API only
          colors: theme?.colors || {},
          fonts: theme?.fonts || {},

          // Branding assets from API only
          images: {
            logo: brandingData?.logo || '',
            hero: brandingData?.heroImage || '',
            favicon: brandingData?.favicon || ''
          },

          // Content from API only
          content: brandingData || {},

          // Site settings from API only
          seo: site?.seo || {},
          features: site?.features || {}
        }

        setBranding(apiBranding)
      } else {
        throw new Error('No branding data received from API')
      }
    } catch (error) {
      console.error('Error loading branding:', error)
      setError('Failed to load branding configuration')
      // Do NOT use fallbacks - leave branding as null
      setBranding(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load branding on mount and set up aggressive polling for real-time updates
  useEffect(() => {
    loadBranding()

    // Set up aggressive polling every 5 seconds for real-time updates
    // This ensures admin changes are reflected almost immediately
    const interval = setInterval(() => {
      loadBranding()
    }, 3600000)

    setPollInterval(interval)

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [loadBranding])

  // Also listen for window focus to refresh branding
  useEffect(() => {
    const handleFocus = () => {
      loadBranding()
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [loadBranding])

  // Listen for localStorage changes to trigger immediate updates
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'branding_updated') {
        console.log('Branding update detected, refreshing...')
        loadBranding()
        // Clear the flag
        localStorage.removeItem('branding_updated')
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Also check for changes in the same tab
    const checkForUpdates = () => {
      if (localStorage.getItem('branding_updated')) {
        console.log('Branding update detected (same tab), refreshing...')
        loadBranding()
        localStorage.removeItem('branding_updated')
      }
    }

    const updateCheckInterval = setInterval(checkForUpdates, 1000)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      clearInterval(updateCheckInterval)
    }
  }, [loadBranding])

  // Refresh branding data manually
  const refreshBranding = useCallback(() => {
    loadBranding()
  }, [loadBranding])

  // Update branding (for admin use) and refresh immediately
  const updateBranding = useCallback((newBranding) => {
    setBranding(prev => ({ ...prev, ...newBranding }))
    // Refresh from API to get latest data
    setTimeout(() => {
      loadBranding()
    }, 1000)
  }, [loadBranding])

  const value = {
    branding,
    isLoading,
    error,
    refreshBranding,
    updateBranding
  }

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  )
}

// Custom hook to use branding context
export const useBranding = () => {
  const context = useContext(BrandingContext)
  if (!context) {
    throw new Error('useBranding must be used within a BrandingProvider')
  }
  return context
}

export default BrandingContext
