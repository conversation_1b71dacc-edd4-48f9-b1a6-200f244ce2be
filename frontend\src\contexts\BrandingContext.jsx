import { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { brandingService } from '../services'
import { BUSINESS_INFO, FALLBACK_IMAGES } from '../utils/constants'

// Default branding configuration (fallback)
const DEFAULT_BRANDING = {
  // Business Information
  businessName: BUSINESS_INFO.name,
  tagline: BUSINESS_INFO.tagline,
  description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
  
  // Contact Information
  phone: BUSINESS_INFO.phone,
  email: BUSINESS_INFO.email,
  address: BUSINESS_INFO.address,
  social: BUSINESS_INFO.social,
  hours: BUSINESS_INFO.hours,
  
  // Brand Colors
  colors: {
    primary: '#008000',      // Green
    secondary: '#f3d016',    // Gold/Yellow
    accent: '#006600',       // Dark Green
    background: '#ffffff',   // White
    text: '#000000',         // Black
    textSecondary: '#666666' // Gray
  },
  
  // Typography
  fonts: {
    primary: 'Inter, system-ui, sans-serif',
    secondary: 'Inter, system-ui, sans-serif',
    heading: 'Inter, system-ui, sans-serif'
  },
  
  // Images and Media
  images: {
    logo: FALLBACK_IMAGES.logo,
    hero: FALLBACK_IMAGES.hero,
    favicon: FALLBACK_IMAGES.logo,
    placeholder: FALLBACK_IMAGES.placeholder
  },
  
  // Content for all pages
  content: {
    // Home page
    heroTitle: 'Beautiful Locs, Beautiful You',
    heroSubtitle: 'Professional loc services and natural hair care by Tina',
    aboutTitle: 'About Goldie Locs',
    aboutText: 'With years of experience in natural hair care and loc maintenance, Tina provides personalized services to help you achieve and maintain beautiful, healthy locs.',

    // Services page
    servicesTitle: 'Our Professional Services',
    servicesSubtitle: 'Expert loc care and natural hair services tailored to your unique needs',
    servicesDescription: 'From starter locs to maintenance and styling, we provide comprehensive care for your natural hair journey.',

    // Consultation page
    consultationTitle: 'Book Your Consultation',
    consultationSubtitle: 'Start your loc journey with a personalized consultation',
    consultationDescription: 'Schedule a one-on-one consultation to discuss your hair goals, assess your hair type, and create a customized care plan.',
    consultationFormTitle: 'Tell Us About Your Hair Goals',
    consultationFormSubtitle: 'Help us understand your needs so we can provide the best service',

    // Shop page
    shopTitle: 'Premium Hair Care Products',
    shopSubtitle: 'Professional-grade products for healthy locs and natural hair',
    shopDescription: 'Discover our curated collection of premium hair care products designed specifically for locs and natural hair.',
    shopFeaturedTitle: 'Featured Products',

    // User Dashboard
    dashboardWelcome: 'Welcome back to your hair care journey!',
    dashboardOverviewTitle: 'Your Hair Care Overview',
    dashboardAppointmentsTitle: 'Your Appointments',
    dashboardOrdersTitle: 'Your Orders',
    dashboardFavoritesTitle: 'Your Favorites',
    dashboardProfileTitle: 'Profile Settings',
    dashboardNextAppointment: 'Your Next Appointment',
    dashboardRecentOrders: 'Recent Orders',
    dashboardLoyaltyTitle: 'Loyalty Rewards',

    // Authentication pages
    loginTitle: 'Welcome Back',
    loginSubtitle: 'Sign in to access your account and manage your appointments',
    signupTitle: 'Join Our Community',
    signupSubtitle: 'Create your account to book appointments and shop products',

    // Cart and checkout
    cartTitle: 'Your Shopping Cart',
    cartEmptyMessage: 'Your cart is empty. Discover our amazing products!',
    cartShippingMessage: 'Free shipping on orders over $50',

    // General UI text
    bookNowButton: 'Book Now',
    shopNowButton: 'Shop Now',
    learnMoreButton: 'Learn More',
    viewAllButton: 'View All',
    continueShoppingButton: 'Continue Shopping',
    proceedToCheckoutButton: 'Proceed to Checkout',
    addToCartButton: 'Add to Cart',
    scheduleConsultationButton: 'Schedule Consultation',

    // Navigation
    navHome: 'Home',
    navServices: 'Services',
    navShop: 'Shop',
    navConsultation: 'Book Consultation',
    navLogin: 'Login',
    navSignup: 'Sign Up',
    navDashboard: 'Dashboard',

    // Footer
    footerQuickLinks: 'Quick Links',
    footerContact: 'Contact',
    footerFollowUs: 'Follow Us',
    footerCopyright: '© 2024 Goldie Locs By Tina. All rights reserved.',

    // Service categories
    serviceLocMaintenance: 'Loc Maintenance',
    serviceLocMaintenanceDesc: 'Professional maintenance to keep your locs healthy and looking their best',
    serviceStarterLocs: 'Starter Locs',
    serviceStarterLocsDesc: 'Begin your loc journey with expert guidance and professional techniques',
    serviceLocStyling: 'Loc Styling',
    serviceLocStylingDesc: 'Creative styling options to showcase your locs for any occasion',
    serviceNaturalHairCare: 'Natural Hair Care',
    serviceNaturalHairCareDesc: 'Comprehensive care for natural hair health and growth',

    // Testimonials and reviews
    testimonialsTitle: 'What Our Clients Say',
    testimonialsSubtitle: 'Real experiences from our satisfied customers',
    reviewsTitle: 'Customer Reviews',
    writeReviewButton: 'Write a Review',

    // Additional authentication content
    forgotPasswordLink: 'Forgot your password?',
    signInButton: 'Sign In',
    signingInText: 'Signing in...',
    noAccountText: 'Don\'t have an account?',
    signUpLink: 'Sign up here',
    createAccountButton: 'Create Account',
    creatingAccountText: 'Creating account...',
    alreadyHaveAccountText: 'Already have an account?',
    signInHereLink: 'Sign in here',
    privacyPolicyLink: 'Privacy Policy',
    termsOfServiceLink: 'Terms of Service',
    helpCenterLink: 'Help Center',

    // Additional cart content
    cartEmptyTitle: 'Your Cart is Empty',

    // Error and loading messages
    loadingMessage: 'Loading...',
    errorMessage: 'Something went wrong. Please try again.',
    notFoundMessage: 'Page not found',
    comingSoonMessage: 'Coming soon!'
  },
  
  // SEO and Meta
  seo: {
    title: 'Goldie Locs By Tina - Professional Loc Services',
    description: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.',
    keywords: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance'
  },
  
  // Features and Settings
  features: {
    onlineBooking: true,
    ecommerce: true,
    loyaltyProgram: true,
    giftCards: true,
    reviews: true,
    blog: false
  }
}

const BrandingContext = createContext({
  branding: DEFAULT_BRANDING,
  isLoading: false,
  error: null,
  refreshBranding: () => {},
  updateBranding: () => {}
})

export const BrandingProvider = ({ children }) => {
  const [branding, setBranding] = useState(DEFAULT_BRANDING)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load branding data from API
  const loadBranding = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await brandingService.getCompleteBranding()
      
      if (response.success && response.data) {
        const { branding: brandingData, business, theme, site } = response.data
        
        // Merge API data with defaults, keeping defaults as fallback
        const mergedBranding = {
          ...DEFAULT_BRANDING,
          
          // Business info from API
          ...(business && {
            businessName: business.name || DEFAULT_BRANDING.businessName,
            tagline: business.tagline || DEFAULT_BRANDING.tagline,
            description: business.description || DEFAULT_BRANDING.description,
            phone: business.phone || DEFAULT_BRANDING.phone,
            email: business.email || DEFAULT_BRANDING.email,
            address: business.address || DEFAULT_BRANDING.address,
            social: { ...DEFAULT_BRANDING.social, ...business.social },
            hours: { ...DEFAULT_BRANDING.hours, ...business.hours }
          }),
          
          // Theme colors from API
          ...(theme && {
            colors: { ...DEFAULT_BRANDING.colors, ...theme.colors },
            fonts: { ...DEFAULT_BRANDING.fonts, ...theme.fonts }
          }),
          
          // Branding assets and content from API
          ...(brandingData && {
            images: {
              ...DEFAULT_BRANDING.images,
              logo: brandingData.logo || DEFAULT_BRANDING.images.logo,
              hero: brandingData.heroImage || DEFAULT_BRANDING.images.hero,
              favicon: brandingData.favicon || DEFAULT_BRANDING.images.favicon
            },
            content: {
              ...DEFAULT_BRANDING.content,
              // Merge all content from API, falling back to defaults
              ...Object.keys(DEFAULT_BRANDING.content).reduce((acc, key) => {
                acc[key] = brandingData[key] || DEFAULT_BRANDING.content[key]
                return acc
              }, {})
            }
          }),
          
          // Site settings from API
          ...(site && {
            seo: { ...DEFAULT_BRANDING.seo, ...site.seo },
            features: { ...DEFAULT_BRANDING.features, ...site.features }
          })
        }
        
        setBranding(mergedBranding)
      } else {
        // API failed, use defaults
        console.warn('Failed to load branding from API, using defaults')
        setBranding(DEFAULT_BRANDING)
      }
    } catch (error) {
      console.error('Error loading branding:', error)
      setError('Failed to load branding configuration')
      // Use defaults on error
      setBranding(DEFAULT_BRANDING)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load branding on mount
  useEffect(() => {
    loadBranding()
  }, [loadBranding])

  // Refresh branding data
  const refreshBranding = useCallback(() => {
    loadBranding()
  }, [loadBranding])

  // Update branding (for admin use)
  const updateBranding = useCallback((newBranding) => {
    setBranding(prev => ({ ...prev, ...newBranding }))
  }, [])

  const value = {
    branding,
    isLoading,
    error,
    refreshBranding,
    updateBranding
  }

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  )
}

// Custom hook to use branding context
export const useBranding = () => {
  const context = useContext(BrandingContext)
  if (!context) {
    throw new Error('useBranding must be used within a BrandingProvider')
  }
  return context
}

export default BrandingContext
