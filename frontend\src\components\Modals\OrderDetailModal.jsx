import React, { useState } from 'react'
import { <PERSON>X, <PERSON>Edit3, FiTrash2, FiShoppingBag, FiUser, FiDollarSign, FiCalendar, FiMapPin, FiPackage } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const OrderDetailModal = ({ 
  order, 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete,
  showToast 
}) => {
  const { branding } = useBranding()
  const [isDeleting, setIsDeleting] = useState(false)

  if (!isOpen || !order) return null

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this order?')) return
    
    setIsDeleting(true)
    try {
      await onDelete(order._id)
      showToast('Order deleted successfully', 'success')
      onClose()
    } catch (error) {
      showToast('Failed to delete order', 'error')
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'processing': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'shipped': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div 
              className="w-10 h-10 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: branding?.colors?.primary + '20' || '#3B82F620' }}
            >
              <FiShoppingBag className="w-5 h-5" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Order Details</h2>
              <p className="text-sm text-gray-500">
                Order #{order.orderNumber || order._id?.slice(-8) || 'N/A'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <FiX className="w-6 h-6 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status and Actions */}
          <div className="flex justify-between items-start">
            <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full border ${getStatusColor(order.status)}`}>
              {order.status || 'Pending'}
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => onEdit(order)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center space-x-2"
              >
                <FiEdit3 className="w-4 h-4" />
                <span>Edit</span>
              </button>
              <button
                onClick={handleDelete}
                disabled={isDeleting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center space-x-2 disabled:opacity-50"
              >
                <FiTrash2 className="w-4 h-4" />
                <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
              </button>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiUser className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Customer Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Customer Name</label>
                <div className="text-gray-900 font-medium">
                  {order.customer?.name || order.customerName || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Email</label>
                <div className="text-gray-900">
                  {order.customer?.email || order.customerEmail || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Phone</label>
                <div className="text-gray-900">
                  {order.customer?.phone || order.customerPhone || 'N/A'}
                </div>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiDollarSign className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Order Summary
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Subtotal</label>
                <div className="text-gray-900 font-medium">
                  {formatCurrency(order.subtotal)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Tax</label>
                <div className="text-gray-900">
                  {formatCurrency(order.tax)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Total</label>
                <div className="text-gray-900 font-bold text-lg">
                  {formatCurrency(order.total)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Payment Method</label>
                <div className="text-gray-900">
                  {order.paymentMethod || 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Payment Status</label>
                <div>
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    order.paymentStatus === 'paid' 
                      ? 'bg-green-100 text-green-800'
                      : order.paymentStatus === 'pending'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {order.paymentStatus || 'Pending'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Order Items */}
          {order.items && order.items.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Order Items ({order.items.length})
              </h3>
              <div className="space-y-3">
                {order.items.map((item, index) => (
                  <div key={index} className="bg-white rounded-lg p-3 border">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{item.name || item.productName}</div>
                        {item.description && (
                          <div className="text-sm text-gray-600 mt-1">{item.description}</div>
                        )}
                        {item.sku && (
                          <div className="text-xs text-gray-500 mt-1">SKU: {item.sku}</div>
                        )}
                      </div>
                      <div className="text-right ml-4">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(item.price)} × {item.quantity}
                        </div>
                        <div className="text-sm font-semibold text-gray-900">
                          {formatCurrency(item.price * item.quantity)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Shipping Information */}
          {order.shippingAddress && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <FiMapPin className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
                Shipping Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">Shipping Address</label>
                  <div className="text-gray-900">
                    {order.shippingAddress}
                  </div>
                </div>
                {order.trackingNumber && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Tracking Number</label>
                    <div className="text-gray-900 font-mono">
                      {order.trackingNumber}
                    </div>
                  </div>
                )}
                {order.shippingMethod && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Shipping Method</label>
                    <div className="text-gray-900">
                      {order.shippingMethod}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Order Timeline */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center mb-4">
              <FiCalendar className="w-5 h-5 mr-2" style={{ color: branding?.colors?.primary || '#3B82F6' }} />
              Order Timeline
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Order Date</label>
                <div className="text-gray-900">
                  {order.createdAt ? new Date(order.createdAt).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : 'N/A'}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                <div className="text-gray-900">
                  {order.updatedAt ? new Date(order.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }) : 'N/A'}
                </div>
              </div>
              {order.shippedAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Shipped Date</label>
                  <div className="text-gray-900">
                    {new Date(order.shippedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              )}
              {order.deliveredAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Delivered Date</label>
                  <div className="text-gray-900">
                    {new Date(order.deliveredAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {order.notes && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Order Notes
              </h3>
              <div className="text-gray-900 bg-white p-3 rounded border">
                {order.notes}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-4 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default OrderDetailModal
