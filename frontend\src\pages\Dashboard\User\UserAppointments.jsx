import { useState } from 'react'
import { FiCalendar, FiClock, FiMapPin, FiUser, FiPhone, FiMail, FiPlus, FiEye, FiEdit3, FiTrash2 } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const UserAppointments = ({ 
  appointments, 
  sectionLoading,
  setShowAddModal,
  setModalType,
  setEditingItem,
  setViewingItem,
  setConfirmDialog,
  handleDeleteAppointment
}) => {
  const { branding } = useBranding()
  const [filterStatus, setFilterStatus] = useState('all')

  const filteredAppointments = appointments.filter(appointment => {
    if (filterStatus === 'all') return true
    return appointment.status === filterStatus
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">My Appointments</h2>
          <p className="text-gray-600 mt-1">View and manage your scheduled appointments</p>
        </div>
        <button
          onClick={() => {
            setShowAddModal(true)
            setModalType('add')
            setEditingItem(null)
          }}
          className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
        >
          <FiPlus className="w-5 h-5 mr-2" />
          Book Appointment
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-wrap gap-2">
          {[
            { id: 'all', label: 'All Appointments', count: appointments.length },
            { id: 'pending', label: 'Pending', count: appointments.filter(a => a.status === 'pending').length },
            { id: 'confirmed', label: 'Confirmed', count: appointments.filter(a => a.status === 'confirmed').length },
            { id: 'completed', label: 'Completed', count: appointments.filter(a => a.status === 'completed').length },
            { id: 'cancelled', label: 'Cancelled', count: appointments.filter(a => a.status === 'cancelled').length }
          ].map((filter) => (
            <button
              key={filter.id}
              onClick={() => setFilterStatus(filter.id)}
              className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                filterStatus === filter.id
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
              }`}
            >
              {filter.label} ({filter.count})
            </button>
          ))}
        </div>
      </div>

      {/* Appointments List */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {sectionLoading.appointments ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
                  <div className="w-16 h-16 bg-gray-300 rounded-xl"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                    <div className="h-3 bg-gray-300 rounded w-24"></div>
                    <div className="h-3 bg-gray-300 rounded w-40"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-6 bg-gray-300 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : filteredAppointments.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {filteredAppointments.map((appointment, index) => (
              <div key={index} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 rounded-xl flex items-center justify-center"
                         style={{ background: `linear-gradient(135deg, ${branding.colors.primary}20, ${branding.colors.secondary}20)` }}>
                      <FiCalendar className="w-8 h-8" style={{ color: branding.colors.secondary }} />
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {appointment.service || appointment.serviceType || 'Service Appointment'}
                      </h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <FiCalendar className="w-4 h-4 mr-2 text-gray-400" />
                          <span>
                            {appointment.date ? new Date(appointment.date).toLocaleDateString() : 'Date not set'}
                          </span>
                        </div>
                        
                        <div className="flex items-center">
                          <FiClock className="w-4 h-4 mr-2 text-gray-400" />
                          <span>{appointment.time || 'Time not set'}</span>
                        </div>
                        
                        {appointment.location && (
                          <div className="flex items-center">
                            <FiMapPin className="w-4 h-4 mr-2 text-gray-400" />
                            <span>{appointment.location}</span>
                          </div>
                        )}
                        
                        {appointment.provider && (
                          <div className="flex items-center">
                            <FiUser className="w-4 h-4 mr-2 text-gray-400" />
                            <span>with {appointment.provider}</span>
                          </div>
                        )}
                      </div>
                      
                      {appointment.notes && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-700">
                            <strong>Notes:</strong> {appointment.notes}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end space-y-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                      {appointment.status || 'Pending'}
                    </span>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setViewingItem(appointment)
                          setEditingItem(appointment)
                          setModalType('view')
                        }}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="View Details"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      
                      {appointment.status !== 'completed' && appointment.status !== 'cancelled' && (
                        <button
                          onClick={() => {
                            setEditingItem(appointment)
                            setModalType('edit')
                            setShowAddModal(true)
                          }}
                          className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer"
                          title="Edit"
                        >
                          <FiEdit3 className="w-4 h-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => {
                          setConfirmDialog({
                            title: 'Cancel Appointment',
                            message: `Are you sure you want to cancel your ${appointment.service || 'appointment'}?`,
                            onConfirm: () => handleDeleteAppointment(appointment.id || index)
                          })
                        }}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer"
                        title="Cancel"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FiCalendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {filterStatus === 'all' ? 'No appointments found' : `No ${filterStatus} appointments`}
            </h3>
            <p className="text-gray-600 mb-6">
              {filterStatus === 'all' 
                ? 'Book your first appointment to get started.'
                : `You don't have any ${filterStatus} appointments.`
              }
            </p>
            {filterStatus === 'all' && (
              <button
                onClick={() => {
                  setShowAddModal(true)
                  setModalType('add')
                  setEditingItem(null)
                }}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                Book First Appointment
              </button>
            )}
          </div>
        )}
      </div>

      {/* Appointment Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiCalendar className="w-5 h-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Appointment Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Book appointments at least 24 hours in advance</li>
                <li>You can reschedule or cancel up to 2 hours before your appointment</li>
                <li>Arrive 10 minutes early for your appointment</li>
                <li>Contact us if you need to make any special arrangements</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserAppointments
